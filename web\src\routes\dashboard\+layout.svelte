<script lang="ts">
  import TeamSwitcher from '$components/layout/TeamSwitcher.svelte';
  import DashboardMainNav from '$components/layout/DashboardNav.svelte';
  import NotificationDropdown from '$components/ui/NotificationDropdown.svelte';
  import FeedbackDropdown from '$components/ui/FeedbackDropdown.svelte';
  import Logo from '$components/ui/Logo.svelte';
  import GlobalSearch from '$components/search/GlobalSearch.svelte';
  import { initResumeParsingHandler } from '$lib/websocket/resume-parsing-handler';
  import { browser } from '$app/environment';
  import WebSocketStatus from '$components/ui/WebSocketStatus.svelte';
  import { notifications } from '$lib/stores/notification';
  import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
  import * as Tooltip from '$lib/components/ui/tooltip';
  import { Button } from '$lib/components/ui/button';
  import { Avatar, AvatarFallback, AvatarImage } from '$lib/components/ui/avatar';
  import { Settings, Gift, CreditCard } from 'lucide-svelte';
  import { goto } from '$app/navigation';
  import websocket from '$lib/websocket/websocket-singleton';

  const { data, children } = $props();
  const user = data?.user;

  // Initialize all singletons and handlers
  if (browser) {
    // Initialize the WebSocket singleton - this will only connect once
    console.log('Initializing WebSocket singleton...');
    websocket.initialize();

    // Fetch notifications from server
    console.log('Fetching notifications from server...');
    notifications.fetchFromServer();

    // Initialize resume parsing handler
    console.log('Initializing resume parsing handler...');
    initResumeParsingHandler();
  }
</script>

<div class="hidden flex-col md:flex">
  <div class="border-border border border-b border-l-0 border-r-0 border-t-0">
    <div class="flex h-16 items-center justify-between px-6">
      <!-- Left Side: Logo dropdown + utilities -->
      <div class="flex items-center gap-4">
        <!-- Logo Dropdown -->
        <DropdownMenu.Root>
          <DropdownMenu.Trigger class="cursor-pointer">
            <Logo class="h-7 w-7" />
          </DropdownMenu.Trigger>
          <DropdownMenu.Content align="start" class="w-56">
            {#if user && 'teamId' in user && user.teamId}
              <TeamSwitcher />
            {/if}
          </DropdownMenu.Content>
        </DropdownMenu.Root>

        <!-- Utilities -->
        <div class="flex items-center gap-4">
          <GlobalSearch placeholder="Search..." />

          <!-- Preferences -->
          <Tooltip.Root>
            <Tooltip.Trigger>
              <button onclick={() => goto('/dashboard/settings')} class="cursor-pointer">
                <Settings class="h-5 w-5 text-gray-500 hover:text-gray-700" />
              </button>
            </Tooltip.Trigger>
            <Tooltip.Content>
              <p>Preferences</p>
            </Tooltip.Content>
          </Tooltip.Root>

          <FeedbackDropdown />
          <NotificationDropdown />

          {#if user.isAdmin}
            <div class="flex items-center">
              <WebSocketStatus showIndicator={false} showText={false} />
            </div>
          {/if}

          <!-- Invite & Earn Button -->
          <Button
            variant="outline"
            size="sm"
            onclick={() => goto('/dashboard/settings/referrals')}
            class="gap-2">
            <Gift class="h-4 w-4" />
            Invite & Earn
          </Button>

          <!-- Upgrade Button -->
          <Button size="sm" onclick={() => goto('/dashboard/settings/billing')} class="gap-2">
            <CreditCard class="h-4 w-4" />
            Upgrade
          </Button>
        </div>
      </div>

      <!-- Center: Navigation -->
      <div class="absolute left-1/2 -translate-x-1/2 transform">
        <DashboardMainNav />
      </div>

      <!-- Right Side: User Avatar -->
      <div class="flex items-center">
        <Tooltip.Provider>
          <Tooltip.Root>
            <Tooltip.Trigger>
              <button onclick={() => goto('/dashboard/settings/account')} class="cursor-pointer">
                <Avatar class="h-8 w-8">
                  <AvatarImage src={user?.image} alt={user?.name || user?.email} />
                  <AvatarFallback>
                    {user?.name
                      ? user.name.charAt(0).toUpperCase()
                      : user?.email?.charAt(0).toUpperCase() || 'U'}
                  </AvatarFallback>
                </Avatar>
              </button>
            </Tooltip.Trigger>
            <Tooltip.Content>
              <p>{user?.name || user?.email}</p>
            </Tooltip.Content>
          </Tooltip.Root>
        </Tooltip.Provider>
      </div>
    </div>
  </div>

  <main>
    {@render children()}
  </main>
</div>
<!-- <Feedback/> -->
