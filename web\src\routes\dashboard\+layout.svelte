<script lang="ts">
  import DashboardMainNav from '$components/layout/DashboardNav.svelte';
  import NotificationDropdown from '$components/ui/NotificationDropdown.svelte';
  import Logo from '$components/ui/Logo.svelte';
  import GlobalSearch from '$components/search/GlobalSearch.svelte';
  import { initResumeParsingHandler } from '$lib/websocket/resume-parsing-handler';
  import { browser } from '$app/environment';
  import WebSocketStatus from '$components/ui/WebSocketStatus.svelte';
  import { notifications } from '$lib/stores/notification';
  import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
  import * as Tooltip from '$lib/components/ui/tooltip';
  import { Button } from '$lib/components/ui/button';
  import { Avatar, AvatarFallback, AvatarImage } from '$lib/components/ui/avatar';
  import {
    Settings,
    Gift,
    CreditCard,
    ChevronDown,
    HelpCircle,
    Users,
    Building,
    LogOut,
  } from 'lucide-svelte';
  import { goto } from '$app/navigation';
  import websocket from '$lib/websocket/websocket-singleton';

  const { data, children } = $props();
  const user = data?.user;

  // Initialize all singletons and handlers
  if (browser) {
    // Initialize the WebSocket singleton - this will only connect once
    console.log('Initializing WebSocket singleton...');
    websocket.initialize();

    // Fetch notifications from server
    console.log('Fetching notifications from server...');
    notifications.fetchFromServer();

    // Initialize resume parsing handler
    console.log('Initializing resume parsing handler...');
    initResumeParsingHandler();
  }
</script>

<div class="hidden flex-col md:flex">
  <div class="primary">
    <Tooltip.Provider>
      <div class="flex h-16 items-center justify-between px-6">
        <!-- Left Side: Logo dropdown + notifications + buttons -->
        <div class="flex items-center gap-4">
          <!-- Logo Dropdown with border and chevron - contains search, settings, help, notifications -->
          <DropdownMenu.Root>
            <DropdownMenu.Trigger
              class="border-border hover:bg-muted flex cursor-pointer items-center gap-2 rounded-md border py-2 pl-2 pr-1">
              <Logo class="h-5 w-5" />
              <ChevronDown class="text-muted-foreground h-4 w-4" />
            </DropdownMenu.Trigger>
            <DropdownMenu.Content align="start" class="w-80" sideOffset={16}>
              <GlobalSearch placeholder="Search..." />
              <DropdownMenu.Separator />

              <DropdownMenu.Item onclick={() => goto('/dashboard')}>Dashboard</DropdownMenu.Item>

              <!-- Settings -->
              <DropdownMenu.Item onclick={() => goto('/dashboard/settings')}>
                <Settings class="mr-2 h-4 w-4" />
                Settings
              </DropdownMenu.Item>

              <!-- Help & Support -->
              <DropdownMenu.Item onclick={() => goto('/help')}>
                <HelpCircle class="mr-2 h-4 w-4" />
                Help & Support
              </DropdownMenu.Item>

              {#if user && 'teamId' in user && user.teamId}
                <DropdownMenu.Separator />
                <DropdownMenu.Item onclick={() => goto('/dashboard/settings/team')}>
                  <Users class="mr-2 h-4 w-4" />
                  Team Settings
                </DropdownMenu.Item>
              {/if}

              <DropdownMenu.Separator />

              <DropdownMenu.Item onclick={() => goto('/dashboard/settings')}>
                <Building class="mr-2 h-4 w-4" />
                Workspace Settings
              </DropdownMenu.Item>

              <DropdownMenu.Separator />

              <DropdownMenu.Item onclick={() => goto('/auth/sign-out')}>
                <LogOut class="mr-2 h-4 w-4" />
                Sign Out
              </DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu.Root>

          <Button
            variant="outline"
            onclick={() => goto('/dashboard/settings/referrals')}
            class="gap-2">
            <Gift class="h-4 w-4" />
            Invite & Earn
          </Button>
        </div>

        <!-- Center: Navigation -->
        <div class="absolute left-1/2 -translate-x-1/2 transform">
          <DashboardMainNav />
        </div>

        <!-- Right Side: User Avatar -->
        <div class="flex flex-row items-center justify-center gap-4 align-middle">
          <!-- Notifications (separate) -->
          <NotificationDropdown />

          <Tooltip.Root>
            <Tooltip.Trigger>
              <button onclick={() => goto('/dashboard/settings/account')} class="cursor-pointer">
                <Avatar class="h-8 w-8">
                  <AvatarImage src={user?.image} alt={user?.name || user?.email} />
                  <AvatarFallback>
                    {user?.name
                      ? user.name.charAt(0).toUpperCase()
                      : user?.email?.charAt(0).toUpperCase() || 'U'}
                  </AvatarFallback>
                </Avatar>
              </button>
            </Tooltip.Trigger>
            <Tooltip.Content>
              <p>{user?.name || user?.email}</p>
            </Tooltip.Content>
          </Tooltip.Root>

          <!-- Upgrade Button -->
          <Button onclick={() => goto('/dashboard/settings/billing')} class="gap-2">Upgrade</Button>
        </div>
      </div>
    </Tooltip.Provider>
  </div>

  <main>
    {@render children()}
  </main>
</div>
<!-- <Feedback/> -->
