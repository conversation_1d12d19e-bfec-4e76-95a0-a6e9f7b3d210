<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import * as Form from '$lib/components/ui/form/index.js';
  import { Switch } from '$lib/components/ui/switch/index.js';
  import { Sun, Moon, Monitor } from 'lucide-svelte';
  import {
    store,
    updateAccessibilitySettings,
    type AccessibilitySettings,
    type ViewMode,
    type ThemeMode,
  } from '$lib/stores/store';
  import { setMode } from 'mode-watcher';
  import { subscribeToPush, unsubscribeFromPush } from '$lib/client/push-notifications';
  import { toast } from 'svelte-sonner';

  const { form, formData } = $props<{ form: any; formData: any }>();

  // Initialize the store from the form data when the component is first loaded
  if ($formData) {
    // Update the theme if provided
    if ($formData.theme) {
      setMode($formData.theme as 'light' | 'dark' | 'system');
    }

    // Update accessibility settings
    const settings: Partial<AccessibilitySettings> = {};

    if ($formData.highContrast !== undefined)
      settings.highContrast = Boolean($formData.highContrast);
    if ($formData.reducedMotion !== undefined)
      settings.reducedMotion = Boolean($formData.reducedMotion);
    if ($formData.largeText !== undefined) settings.largeText = Boolean($formData.largeText);
    if ($formData.screenReader !== undefined)
      settings.screenReader = Boolean($formData.screenReader);

    // UI Preferences
    if ($formData.viewMode !== undefined) settings.viewMode = $formData.viewMode as ViewMode;

    if (Object.keys(settings).length > 0) {
      updateAccessibilitySettings(settings);
    }
  }

  // Handle theme change from the UI
  function handleThemeChange(value: ThemeMode) {
    console.log('handleThemeChange called with:', value);

    // Update the store first (this will be saved to localStorage)
    updateAccessibilitySettings({ theme: value });

    // Apply the theme using mode-watcher
    setMode(value);

    // Update form data
    formData.update((f: any) => ({ ...f, theme: value }));

    // Submit the form
    const submitButton = document.getElementById('submit-button') as HTMLButtonElement;
    if (submitButton) submitButton.click();
  }

  // Handle accessibility setting change
  function handleAccessibilityChange(setting: keyof AccessibilitySettings, value: any) {
    console.log(`Changing ${setting} to ${value}`);
    updateAccessibilitySettings({ [setting]: value });

    // Update form data
    formData.update((f: any) => ({ ...f, [setting]: value }));

    // Submit the form
    const submitButton = document.getElementById('submit-button') as HTMLButtonElement;
    if (submitButton) submitButton.click();
  }
</script>

<div class="border-border border-b px-6 py-4">
  <h4 class="text-md font-normal">Accessibility Settings</h4>
  <p class="text-muted-foreground text-sm">Customize your experience for better accessibility.</p>
</div>

<div class="grid gap-6 p-6 sm:grid-cols-2">
  <Form.Field {form} name="theme">
    <div class="space-y-2">
      <div class="font-medium">Theme Preference</div>
      <Form.Description>Choose your preferred theme for the application</Form.Description>
      <div class="flex flex-col gap-4 pt-2 sm:flex-row">
        <!-- Light Theme Option -->
        <button
          type="button"
          class="hover:bg-accent flex cursor-pointer flex-col items-center justify-between rounded-md border-2 p-4 {$store
            ?.account?.accessibility?.theme === 'light'
            ? 'border-primary'
            : 'border-muted'}"
          onclick={() => handleThemeChange('light')}>
          <Sun class="mb-2 h-6 w-6 text-yellow-500" />
          <span>Light</span>
        </button>

        <!-- Dark Theme Option -->
        <button
          type="button"
          class="hover:bg-accent flex cursor-pointer flex-col items-center justify-between rounded-md border-2 p-4 {$store
            ?.account?.accessibility?.theme === 'dark'
            ? 'border-primary'
            : 'border-muted'}"
          onclick={() => handleThemeChange('dark')}>
          <Moon class="mb-2 h-6 w-6 text-blue-400" />
          <span>Dark</span>
        </button>

        <!-- System Theme Option -->
        <button
          type="button"
          class="hover:bg-accent flex cursor-pointer flex-col items-center justify-between rounded-md border-2 p-4 {$store
            ?.account?.accessibility?.theme === 'system'
            ? 'border-primary'
            : 'border-muted'}"
          onclick={() => handleThemeChange('system')}>
          <Monitor class="mb-2 h-6 w-6 text-gray-500" />
          <span>System</span>
        </button>
      </div>
    </div>
    <Form.FieldErrors />
  </Form.Field>
  <Form.Field {form} name="highContrast">
    <div class="flex items-center justify-between">
      <div class="space-y-0.5">
        <div class="font-medium">High Contrast Mode</div>
        <Form.Description>Increase contrast for better visibility</Form.Description>
      </div>
      <Form.Control>
        <Switch
          checked={Boolean($formData.highContrast)}
          onCheckedChange={(checked) => handleAccessibilityChange('highContrast', checked)} />
      </Form.Control>
    </div>
    <Form.FieldErrors />
  </Form.Field>

  <Form.Field {form} name="pushNotifications">
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <div class="font-medium">Push Notifications</div>
          <Form.Description>Enable or disable browser push notifications</Form.Description>
        </div>
        <Form.Control>
          <Switch
            checked={Boolean($formData.pushNotifications)}
            onCheckedChange={async (checked) => {
              if (checked) {
                // Subscribe to push notifications
                const result = await subscribeToPush();
                if (result.success) {
                  // Only update form AFTER subscription is successful
                  formData.update((f: any) => ({ ...f, pushNotifications: true }));
                  const submitButton = document.getElementById(
                    'submit-button'
                  ) as HTMLButtonElement;
                  if (submitButton) submitButton.click();
                  toast.success('Push notifications enabled successfully!');
                } else {
                  // Keep the switch off if subscription failed
                  formData.update((f: any) => ({ ...f, pushNotifications: false }));
                  toast.error((result as any).error || 'Failed to enable push notifications');
                }
              } else {
                // Unsubscribe from push notifications
                const result = await unsubscribeFromPush();
                if (result.success) {
                  // Update form and save to DB
                  formData.update((f: any) => ({ ...f, pushNotifications: false }));
                  const submitButton = document.getElementById(
                    'submit-button'
                  ) as HTMLButtonElement;
                  if (submitButton) submitButton.click();
                  toast.success('Push notifications disabled successfully');
                } else {
                  // Reset the switch if unsubscribing failed
                  formData.update((f: any) => ({ ...f, pushNotifications: true }));
                  toast.error((result as any).error || 'Failed to disable push notifications');
                }
              }
            }} />
        </Form.Control>
      </div>
    </div>
    <Form.FieldErrors />
  </Form.Field>
</div>
