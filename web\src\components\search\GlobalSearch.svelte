<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { goto } from '$app/navigation';
  import { browser } from '$app/environment';
  // Removed Input import as we're using a button now
  import * as Avatar from '$lib/components/ui/avatar/index.js';
  import * as Command from '$lib/components/ui/command/index.js';
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import * as ScrollArea from '$lib/components/ui/scroll-area/index.js';
  import { Search, Briefcase, FileText, X, Star } from 'lucide-svelte';
  import { activeDropdownId } from '$lib/stores/dropdown-store';

  // Focus action for the search input
  function focusOnOpen(node: HTMLElement) {
    // Initial focus when the component is mounted
    if (dialogOpen) {
      setTimeout(() => node.focus(), 50);
    }

    // Update function to handle changes to dialogOpen
    return {
      update() {
        if (dialogOpen) {
          // Use setTimeout to ensure the dialog is fully rendered
          setTimeout(() => node.focus(), 50);
        }
      },
    };
  }

  // Props
  export const placeholder = 'Search...'; // Changed to const as it's no longer used in the template
  export let className = '';
  // userData is no longer needed as we're using the server API which has access to the user context

  let searchQuery = '';
  let dialogOpen = false;
  let searchResults: any = {
    users: [],
    jobs: [],
    documents: [],
  };
  let isLoading = false;

  // Recent searches functionality
  interface RecentSearch {
    query: string;
    favorite: boolean;
  }
  let recentSearches: RecentSearch[] = [];

  // Load recent searches from localStorage on mount
  onMount(() => {
    if (browser) {
      const savedSearches = localStorage.getItem('recentSearches');
      if (savedSearches) {
        try {
          recentSearches = JSON.parse(savedSearches);
        } catch (e) {
          console.error('Failed to parse recent searches:', e);
          recentSearches = [];
        }
      }
    }
  });

  // Save a search to recent searches
  function saveToRecentSearches(query: string) {
    if (!browser || query.length < 2) return;

    // Remove the query if it already exists (to avoid duplicates)
    recentSearches = recentSearches.filter((s) => s.query !== query);

    // Add the new query to the beginning of the array
    recentSearches = [{ query, favorite: false }, ...recentSearches.slice(0, 9)]; // Keep only the 10 most recent

    // Save to localStorage
    localStorage.setItem('recentSearches', JSON.stringify(recentSearches));
  }

  // Remove a search from recent searches
  function removeFromRecentSearches(search: RecentSearch) {
    if (!browser) return;

    recentSearches = recentSearches.filter((s) => s.query !== search.query);
    localStorage.setItem('recentSearches', JSON.stringify(recentSearches));
  }

  // Toggle favorite status of a search
  function toggleFavorite(search: RecentSearch) {
    if (!browser) return;

    recentSearches = recentSearches.map((s) => {
      if (s.query === search.query) {
        return { ...s, favorite: !s.favorite };
      }
      return s;
    });

    localStorage.setItem('recentSearches', JSON.stringify(recentSearches));
  }

  // Handle search input changes
  async function handleSearch() {
    if (searchQuery.length < 2) {
      searchResults = { users: [], jobs: [], documents: [] };
      return;
    }

    isLoading = true;
    dialogOpen = true;

    // Close any open dropdowns by setting this dialog as active
    activeDropdownId.set(searchDialogId);

    try {
      // Call our API endpoint to search across all data types
      const response = await fetch('/api/search/global', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: searchQuery,
          limit: 10,
        }),
      });

      if (!response.ok) {
        throw new Error(`Search failed with status: ${response.status}`);
      }

      const data = await response.json();

      // Format the results
      searchResults = {
        users: data.users.hits || [],
        jobs: data.jobs.hits || [],
        documents: data.documents.hits || [],
      };

      // Save successful search to recent searches
      if (
        searchResults.users.length > 0 ||
        searchResults.jobs.length > 0 ||
        searchResults.documents.length > 0
      ) {
        saveToRecentSearches(searchQuery);
      }
    } catch (error) {
      console.error('Search error:', error);
      // Reset results on error
      searchResults = { users: [], jobs: [], documents: [] };
    } finally {
      isLoading = false;
    }
  }

  // Handle keyboard navigation
  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      closeDialog();
    } else if (event.key === 'ArrowDown' || event.key === 'ArrowUp') {
      // Let the Command component handle this
      event.preventDefault();
    }
  }

  // Handle selection
  function handleSelect(type: string, item: any) {
    // Save the current search query to recent searches
    if (searchQuery.length >= 2) {
      saveToRecentSearches(searchQuery);
    }

    switch (type) {
      case 'user':
        goto(`/dashboard/admin/users/${item.id}`);
        break;
      case 'job':
        goto(`/dashboard/jobs/${item.id}`);
        break;
      case 'document':
        goto(`/dashboard/documents/${item.id}`);
        break;
    }

    closeDialog();
  }

  // Close dialog
  function closeDialog() {
    dialogOpen = false;
    searchQuery = '';

    // Clear this dialog from being active in the dropdown store
    activeDropdownId.update((currentId) => (currentId === searchDialogId ? null : currentId));
  }

  // Open search dialog when pressing Ctrl+K or Cmd+K
  function handleGlobalKeyDown(event: KeyboardEvent) {
    if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
      event.preventDefault();

      // Close any open dropdowns by setting this dialog as active
      activeDropdownId.set(searchDialogId);

      dialogOpen = true;
    }
  }

  // Generate a unique ID for the search dialog
  const searchDialogId = `global-search-${Math.random().toString(36).substring(2, 9)}`;

  // Subscribe to the active dropdown store
  let unsubscribe: (() => void) | null = null;

  onMount(() => {
    if (browser) {
      window.addEventListener('keydown', handleGlobalKeyDown);

      // Subscribe to the active dropdown store
      unsubscribe = activeDropdownId.subscribe((activeId) => {
        // If another dropdown is active and the search dialog is open, close it
        if (activeId && activeId !== searchDialogId && dialogOpen) {
          closeDialog();
        }
      });
    }
  });

  onDestroy(() => {
    if (browser) {
      window.removeEventListener('keydown', handleGlobalKeyDown);

      // Clean up subscription
      if (unsubscribe) {
        unsubscribe();
      }
    }
  });

  // Watch for search query changes
  $: if (searchQuery !== undefined) {
    handleSearch();
  }
</script>

<style>
  /* Add any custom styles here */
</style>

<div class="relative {className}">
  <button
    type="button"
    class="border-input bg-background text-muted-foreground hover:bg-accent hover:text-accent-foreground flex h-9 w-full items-center gap-2 rounded-md border px-3 py-2 text-sm shadow-sm transition-colors"
    on:click={() => {
      // Close any open dropdowns by setting this dialog as active
      activeDropdownId.set(searchDialogId);
      dialogOpen = true;
    }}>
    <Search class="h-4 w-4" />
    <span class="hidden md:inline-flex">Search</span>
    <kbd
      class="bg-muted ml-auto hidden h-5 select-none items-center gap-1 rounded border px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex">
      {#if browser && /Mac|iPod|iPhone|iPad/.test(navigator.userAgent)}
        <span class="text-xs">⌘</span>
      {:else}
        <span class="text-xs">Ctrl</span>
      {/if}
      K
    </kbd>
  </button>

  <Dialog.Root bind:open={dialogOpen}>
    <Dialog.Portal>
      <Dialog.Overlay />
      <Dialog.Content class="gap-0 p-0 sm:max-w-[90vw] md:max-w-[65vw] lg:max-w-[50vw]">
        <div class="flex h-[80vh] flex-col border-none">
          <div class="flex items-center border-b p-3">
            <Search class="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <input
              type="search"
              placeholder="Search users, jobs, and documents..."
              bind:value={searchQuery}
              class="placeholder:text-muted-foreground flex-1 border-none bg-transparent py-3 text-sm outline-none focus:ring-0 disabled:cursor-not-allowed disabled:opacity-50"
              on:keydown={handleKeyDown}
              use:focusOnOpen />
            <button
              on:click={closeDialog}
              class="cursor-point bg-muted pointer-events-none hidden h-5 select-none items-center gap-1 rounded border px-1.5 font-mono text-[10px] font-medium opacity-50 sm:flex">
              <span class="text-xs">ESC</span>
              <X class="h-4 w-4" />
            </button>
          </div>

          <div class="flex-1 overflow-hidden">
            <ScrollArea.Root class="h-full">
              <Command.Root class="h-full">
                <Command.List class="h-full">
                  {#if isLoading}
                    <Command.Loading>
                      <div class="flex items-center justify-center p-6">
                        <div
                          class="border-t-primary border-muted h-5 w-5 animate-spin rounded-full border-2">
                        </div>
                        <span class="text-muted-foreground ml-2 text-sm">Searching...</span>
                      </div>
                    </Command.Loading>
                  {:else if searchQuery.length < 2}
                    {#if recentSearches.length > 0}
                      <div class="p-4">
                        <h2 class="text-muted-foreground mb-2 text-xs font-medium">Recent</h2>
                        <div class="space-y-1">
                          {#each [...recentSearches].sort((a, b) => Number(b.favorite) - Number(a.favorite)) as search}
                            <div
                              class="hover:bg-accent flex items-center justify-between rounded-md px-2 py-1.5">
                              <div class="flex flex-1 items-center gap-2">
                                <button
                                  type="button"
                                  class="flex h-4 w-4 items-center justify-center"
                                  on:click={() => toggleFavorite(search)}
                                  aria-label={search.favorite
                                    ? 'Remove from favorites'
                                    : 'Add to favorites'}>
                                  <Star
                                    class="h-3 w-3 flex-shrink-0 {search.favorite
                                      ? 'text-warning'
                                      : 'text-muted-foreground opacity-50'}" />
                                </button>
                                <button
                                  type="button"
                                  class="flex-1 cursor-pointer text-left text-sm"
                                  on:click={() => {
                                    searchQuery = search.query;
                                  }}>
                                  {search.query}
                                </button>
                              </div>
                              <button
                                type="button"
                                class="hover:bg-muted ml-2 flex h-5 w-5 items-center justify-center rounded-full"
                                on:click={() => removeFromRecentSearches(search)}
                                aria-label="Remove from recent searches">
                                <X class="h-3 w-3 opacity-50" />
                              </button>
                            </div>
                          {/each}
                        </div>
                      </div>
                    {:else}
                      <Command.Empty>
                        <div class="p-6 text-center text-sm">
                          Type at least 2 characters to search
                        </div>
                      </Command.Empty>
                    {/if}
                  {:else if searchResults.users.length === 0 && searchResults.jobs.length === 0 && searchResults.documents.length === 0}
                    <Command.Empty>
                      <div class="p-6 text-center">
                        <p class="text-muted-foreground text-sm">No results for "{searchQuery}"</p>
                        <div class="mt-4">
                          <h3 class="mb-2 text-sm font-semibold">Try searching for:</h3>
                          <div class="text-muted-foreground space-y-2 text-sm">
                            <button
                              type="button"
                              class="hover:bg-accent mt-2 flex w-full cursor-pointer items-center rounded-md px-2 py-1 text-left"
                              on:click={() => (searchQuery = 'users')}>
                              <span class="ml-2">Users</span>
                            </button>
                            <button
                              type="button"
                              class="hover:bg-accent mt-2 flex w-full cursor-pointer items-center rounded-md px-2 py-1 text-left"
                              on:click={() => (searchQuery = 'jobs')}>
                              <span class="ml-2">Jobs</span>
                            </button>
                            <button
                              type="button"
                              class="hover:bg-accent mt-2 flex w-full cursor-pointer items-center rounded-md px-2 py-1 text-left"
                              on:click={() => (searchQuery = 'documents')}>
                              <span class="ml-2">Documents</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    </Command.Empty>
                  {:else}
                    {#if searchResults.users.length > 0}
                      <Command.Group
                        heading="Users"
                        class="text-muted-foreground px-2 py-1.5 text-xs font-medium">
                        {#each searchResults.users as user}
                          <Command.Item
                            onSelect={() => handleSelect('user', user)}
                            class="aria-selected:bg-accent aria-selected:text-accent-foreground flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none">
                            <div class="flex items-center gap-2">
                              <Avatar.Root class="h-6 w-6">
                                {#if user.image}
                                  <Avatar.Image src={user.image} alt={user.name || 'User'} />
                                {/if}
                                <Avatar.Fallback
                                  class="border-border bg-muted rounded-full border text-xs">
                                  {user.name?.charAt(0) || 'U'}
                                </Avatar.Fallback>
                              </Avatar.Root>
                              <div>
                                <div class="font-medium">{user.name || 'Unknown User'}</div>
                                <div class="text-muted-foreground text-xs">{user.email}</div>
                              </div>
                            </div>
                            <div class="ml-auto">
                              <span
                                class="bg-muted text-muted-foreground rounded-full px-2 py-0.5 text-xs font-medium">
                                {user.role}
                              </span>
                            </div>
                          </Command.Item>
                        {/each}
                      </Command.Group>
                    {/if}

                    {#if searchResults.jobs.length > 0}
                      <Command.Group
                        heading="Jobs"
                        class="text-muted-foreground px-2 py-1.5 text-xs font-medium">
                        {#each searchResults.jobs as job}
                          <Command.Item
                            onSelect={() => handleSelect('job', job)}
                            class="aria-selected:bg-accent aria-selected:text-accent-foreground flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none">
                            <Briefcase class="mr-2 h-4 w-4 shrink-0 opacity-50" />
                            <div>
                              <div class="font-medium">{job.title}</div>
                              <div class="text-muted-foreground text-xs">{job.company}</div>
                            </div>
                          </Command.Item>
                        {/each}
                      </Command.Group>
                    {/if}

                    {#if searchResults.documents.length > 0}
                      <Command.Group
                        heading="Documents"
                        class="text-muted-foreground px-2 py-1.5 text-xs font-medium">
                        {#each searchResults.documents as document}
                          <Command.Item
                            onSelect={() => handleSelect('document', document)}
                            class="aria-selected:bg-accent aria-selected:text-accent-foreground flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none">
                            <FileText class="mr-2 h-4 w-4 shrink-0 opacity-50" />
                            <div>
                              <div class="font-medium">{document.title}</div>
                              <div class="text-muted-foreground text-xs">{document.type}</div>
                            </div>
                          </Command.Item>
                        {/each}
                      </Command.Group>
                    {/if}
                  {/if}
                </Command.List>
              </Command.Root>
            </ScrollArea.Root>
          </div>
          <!-- Footer removed -->
        </div>
      </Dialog.Content>
    </Dialog.Portal>
  </Dialog.Root>
</div>
