<script lang="ts">
  import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
  import * as Avatar from '$lib/components/ui/avatar';
  import { Button } from '$lib/components/ui/button';
  import { goto } from '$app/navigation';
  import { toast } from 'svelte-sonner';
  import { KeyboardShortcutsDialog } from '$components/keyboard-shortcuts';
  import {
    CirclePlus,
    CreditCard,
    Keyboard,
    LogOut,
    Mail,
    MessageSquare,
    Plus,
    Settings,
    User,
    Users,
  } from 'lucide-svelte';
  import userProfileStore from '$lib/stores/user-profile';

  const { userData = null } = $props();

  // State for keyboard shortcuts dialog
  let showKeyboardShortcutsDialog = $state(false);

  // Define navigation items arrays
  const accountNavItems = [
    {
      label: 'Profile',
      icon: User,
      href: '/dashboard/settings/profile',
      shortcut: '⇧⌘P',
      shortcutKey: 'P',
      onClick: () => goto('/dashboard/settings/profile'),
    },
    {
      label: 'Billing',
      icon: CreditCard,
      href: '/dashboard/settings/billing',
      shortcut: '⌘B',
      shortcutKey: 'B',
      onClick: () => goto('/dashboard/settings/billing'),
    },
    {
      label: 'Settings',
      icon: Settings,
      href: '/dashboard/settings',
      shortcut: '⌘S',
      shortcutKey: 'S',
      onClick: () => goto('/dashboard/settings'),
    },
    {
      label: 'Keyboard shortcuts',
      icon: Keyboard,
      shortcut: '⌘/',
      onClick: openKeyboardShortcutsDialog,
    },
  ];

  const teamNavItems = [
    {
      label: 'Team',
      icon: Users,
      onClick: null,
    },
    {
      label: 'New Team',
      icon: Plus,
      shortcut: '⌘+T',
      onClick: null,
    },
  ];

  const inviteUserItems = [
    {
      label: 'Email',
      icon: Mail,
      onClick: null,
    },
    {
      label: 'Message',
      icon: MessageSquare,
      onClick: null,
    },
    {
      label: 'More...',
      icon: CirclePlus,
      onClick: null,
    },
  ];

  function openKeyboardShortcutsDialog() {
    console.log('Opening keyboard shortcuts dialog');
    showKeyboardShortcutsDialog = !showKeyboardShortcutsDialog;
    console.log('Keyboard shortcuts dialog state:', showKeyboardShortcutsDialog);
  }

  const logoutNavItem = {
    label: 'Log out',
    icon: LogOut,
    shortcut: '⇧⌘Q',
    shortcutKey: 'Q',
    onClick: logout,
  };

  async function logout() {
    try {
      const res = await fetch('/api/auth/logout', { method: 'POST' });
      if (res.ok) {
        toast.success('Logged out successfully');
        window.location.href = '/auth/sign-in';
      } else {
        toast.error('Failed to log out', {
          description: 'Please try again later',
        });
      }
    } catch (error) {
      toast.error('Network error', {
        description: 'Could not connect to the server',
      });
      console.error('Logout error:', error);
    }
  }
</script>

<DropdownMenu.Root>
  <DropdownMenu.Trigger>
    <Button variant="ghost" class="relative h-8 w-8 rounded-full align-middle">
      <Avatar.Root class="h-8 w-8">
        <Avatar.Image
          src={$userProfileStore.image || userData?.image || null}
          alt={$userProfileStore.name || userData?.name || 'User'} />
        <Avatar.Fallback class="border-border bg-muted rounded-full border"
          >{($userProfileStore.name || userData?.name || 'U').charAt(0)}</Avatar.Fallback>
      </Avatar.Root>
    </Button>
  </DropdownMenu.Trigger>
  <DropdownMenu.Content class="w-56 rounded-none" sideOffset={16} align="end">
    <DropdownMenu.Label>My Account</DropdownMenu.Label>
    <DropdownMenu.Separator class="border-border border-b" />
    <DropdownMenu.Group>
      {#each accountNavItems as { label, icon, shortcut, onClick }}
        <DropdownMenu.Item
          class="hover:bg-muted hover:text-foreground cursor-pointer"
          onclick={onClick}>
          {@const Icon = icon}
          <Icon class="mr-2 size-4" />
          <span>{label}</span>
          {#if shortcut}
            <DropdownMenu.Shortcut>{shortcut}</DropdownMenu.Shortcut>
          {/if}
        </DropdownMenu.Item>
      {/each}
    </DropdownMenu.Group>
    {#if userData?.teamId}
      <DropdownMenu.Separator class="border-border border-b" />
      <DropdownMenu.Group>
        {#each teamNavItems as { label, icon, shortcut, onClick }}
          <DropdownMenu.Item
            class="hover:bg-muted hover:text-foreground cursor-pointer"
            onclick={onClick}>
            {@const Icon = icon}
            <Icon class="mr-2 size-4" />
            <span>{label}</span>
            {#if shortcut}
              <DropdownMenu.Shortcut>{shortcut}</DropdownMenu.Shortcut>
            {/if}
          </DropdownMenu.Item>
        {/each}

        <DropdownMenu.Sub>
          <DropdownMenu.SubTrigger>Invite users</DropdownMenu.SubTrigger>
          <DropdownMenu.SubContent>
            {#each inviteUserItems as { label, icon, onClick }}
              <DropdownMenu.Item onclick={onClick}>
                {#if icon}
                  <div class="mr-2 size-4">
                    {icon}
                  </div>
                {/if}
                <span>{label}</span>
              </DropdownMenu.Item>
            {/each}
          </DropdownMenu.SubContent>
        </DropdownMenu.Sub>
      </DropdownMenu.Group>
    {/if}

    <DropdownMenu.Separator class="border-border border-t" />

    <DropdownMenu.Item
      class="hover:bg-muted hover:text-foreground cursor-pointer"
      onclick={logoutNavItem.onClick}>
      {@const Icon = logoutNavItem.icon}
      <Icon class="mr-2 size-4" />
      <span>{logoutNavItem.label}</span>
      {#if logoutNavItem.shortcut}
        <DropdownMenu.Shortcut>{logoutNavItem.shortcut}</DropdownMenu.Shortcut>
      {/if}
    </DropdownMenu.Item>
  </DropdownMenu.Content>
</DropdownMenu.Root>

<!-- Keyboard Shortcuts Dialog -->
<KeyboardShortcutsDialog bind:open={showKeyboardShortcutsDialog} />
