<script lang="ts">
  import { cn } from '$lib/utils.js';
  import { page } from '$app/stores';

  let className: string | undefined | null = undefined;
  export { className as class };

  // Navigation items array
  const navItems = [
    { href: '/dashboard/jobs', label: 'Jobs' },
    { href: '/dashboard/automation', label: 'Automation' },
    { href: '/dashboard/matches', label: 'Matches' },
    { href: '/dashboard/tracker', label: 'Tracker' },
    { href: '/dashboard/documents', label: 'Documents' },
  ];

  // Function to determine if a nav item is active
  function isActive(href: string, exact = false) {
    if (exact) {
      return $page.url.pathname === href;
    }
    return $page.url.pathname.includes(href);
  }
</script>

<nav class={cn('bg-muted/20 flex items-center rounded-lg p-1', className)}>
  {#each navItems as { href, label, exact }}
    <a
      {href}
      class={cn(
        'relative rounded-md px-3 py-1.5 text-sm font-medium transition-all duration-200',
        isActive(href, exact)
          ? 'bg-primary text-primary-foreground shadow-sm'
          : 'text-muted-foreground hover:text-foreground hover:bg-muted'
      )}>
      {label}
    </a>
  {/each}
</nav>
