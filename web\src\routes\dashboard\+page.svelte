<script>
  import SEO from '$components/shared/SEO.svelte';
  import PricingModal from '$components/modals/Pricing.svelte';
  import Button from '$lib/components/ui/button/button.svelte';
  import { CreditCard, Activity, Briefcase, Download } from 'lucide-svelte';
  import * as Card from '$lib/components/ui/card/index';

  import { onMount } from 'svelte';
  import WelcomeToast from '$components/onboarding/WelcomeToast.svelte';
  import WelcomeSlider from '$components/onboarding/WelcomeSlider.svelte';
  import ReferralCard from '$components/dashboard/ReferralCard.svelte';
  import ResolvedKeywords from '$lib/components/automation/ResolvedKeywords.svelte';
  // Chart components removed - using inline charts instead

  let { data } = $props();

  // For modal controls
  let showPricing = $state(false);
  let showWelcomeSlider = $state(false);

  // Check if this is a new user and refresh session if needed
  onMount(async () => {
    // Check for checkout success
    const urlParams = new URLSearchParams(window.location.search);
    const checkoutStatus = urlParams.get('checkout');

    // If checkout was successful, refresh the session to update the user's role
    if (checkoutStatus === 'success') {
      try {
        const response = await fetch('/api/auth/refresh-session', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const result = await response.json();
          console.log('Session refreshed with updated role:', result.user.role);

          // Remove the query parameter
          const url = new URL(window.location.href);
          url.searchParams.delete('checkout');
          window.history.replaceState({}, '', url);

          // Reload the page to reflect the changes
          window.location.reload();
        }
      } catch (error) {
        console.error('Failed to refresh session:', error);
      }
    }

    // Check for onboarding
    if (typeof localStorage !== 'undefined') {
      const onboardingCompleted = localStorage.getItem('onboardingCompleted');
      if (!onboardingCompleted && data.user) {
        // Show welcome slider after a short delay
        setTimeout(() => {
          showWelcomeSlider = true;
        }, 1500);
      }
    }
  });
</script>

<SEO
  title="Dashboard | Hirli"
  description="Manage your job applications, track your progress, and get insights on your job search with Hirli's intelligent dashboard."
  keywords="job dashboard, job applications, job tracking, career management, application analytics, job search progress" />

<div class="border-border grid divide-x border-b md:grid-cols-2 lg:grid-cols-4">
  <div class="p-4">
    <div class="flex flex-row items-center justify-between space-y-0 pb-2">
      <h3 class="text-sm font-medium">Automation Runs</h3>
      <Activity class="text-muted-foreground h-4 w-4" />
    </div>
    <div>
      <div class="text-2xl font-bold">{data.automationStats?.totalRuns || 0}</div>
      <p class="text-muted-foreground text-xs">
        {data.automationStats?.completedRuns || 0} completed, {data.automationStats?.runningRuns ||
          0} running
      </p>
    </div>
  </div>
  <div class="p-4">
    <div class="flex flex-row items-center justify-between space-y-0 pb-2">
      <h3 class="text-sm font-medium">Applications</h3>
      <Briefcase class="text-muted-foreground h-4 w-4" />
    </div>
    <div>
      <div class="text-2xl font-bold">{data.applicationStats?.total || 0}</div>
      <p class="text-muted-foreground text-xs">
        {data.applicationStats?.byStatus?.interview || 0} interviews
      </p>
    </div>
  </div>
  <div class="p-4">
    <div class="flex flex-row items-center justify-between space-y-0 pb-2">
      <h3 class="text-sm font-medium">Jobs Found</h3>
      <Briefcase class="text-muted-foreground h-4 w-4" />
    </div>
    <div>
      <div class="text-2xl font-bold">{data.automationStats?.totalJobsFound || 0}</div>
      <p class="text-muted-foreground text-xs">
        {Math.round(data.automationStats?.avgProgress || 0)}% avg progress
      </p>
    </div>
  </div>
  <div class="p-4">
    <div class="flex flex-row items-center justify-between space-y-0 pb-2">
      <h3 class="text-sm font-medium">Subscription Usage</h3>
      <CreditCard class="text-muted-foreground h-4 w-4" />
    </div>
    <div>
      <div class="text-2xl font-bold">{data.usage?.used || 0}</div>
      <p class="text-muted-foreground text-xs">
        {data.usage?.limit !== null ? `${data.usage?.remaining || 0} remaining` : 'Unlimited'}
      </p>
    </div>
  </div>
</div>
<div class="grid gap-4 p-4 md:grid-cols-2 lg:grid-cols-7">
  <Card.Root class="col-span-4">
    <Card.Header>
      <Card.Title>Automation Status</Card.Title>
      <Card.Description>Distribution of automation runs by status</Card.Description>
    </Card.Header>
    <Card.Content>
      <div class="space-y-4">
        <!-- Completed Runs -->
        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium">Completed</span>
            <span class="text-muted-foreground text-sm"
              >{data.automationStats?.completedRuns || 0} runs</span>
          </div>
          <div class="bg-muted h-2 w-full overflow-hidden rounded-full">
            <div
              class="bg-success h-full rounded-full"
              style="width: {data.automationStats?.totalRuns
                ? (data.automationStats.completedRuns / data.automationStats.totalRuns) * 100
                : 0}%">
            </div>
          </div>
        </div>

        <!-- Running Runs -->
        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium">Running</span>
            <span class="text-muted-foreground text-sm"
              >{data.automationStats?.runningRuns || 0} runs</span>
          </div>
          <div class="bg-muted h-2 w-full overflow-hidden rounded-full">
            <div
              class="bg-primary h-full rounded-full"
              style="width: {data.automationStats?.totalRuns
                ? (data.automationStats.runningRuns / data.automationStats.totalRuns) * 100
                : 0}%">
            </div>
          </div>
        </div>

        <!-- Pending Runs -->
        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium">Pending</span>
            <span class="text-muted-foreground text-sm"
              >{data.automationStats?.pendingRuns || 0} runs</span>
          </div>
          <div class="bg-muted h-2 w-full overflow-hidden rounded-full">
            <div
              class="bg-warning h-full rounded-full"
              style="width: {data.automationStats?.totalRuns
                ? (data.automationStats.pendingRuns / data.automationStats.totalRuns) * 100
                : 0}%">
            </div>
          </div>
        </div>
      </div>
    </Card.Content>
  </Card.Root>
  <Card.Root class="col-span-3">
    <Card.Header>
      <Card.Title>Recent Automation Runs</Card.Title>
      <Card.Description>Your most recent automation runs</Card.Description>
    </Card.Header>
    <Card.Content>
      {#if !data.recentAutomationRuns?.length}
        <div
          class="text-muted-foreground flex flex-col items-center justify-center py-6 text-center">
          <p>No automation runs yet</p>
          <p class="mt-1 text-sm">Create your first automation to get started</p>
        </div>
      {:else}
        <div class="space-y-4">
          {#each data.recentAutomationRuns.slice(0, 3) as run}
            <div class="flex items-center justify-between">
              <div class="space-y-1">
                <p class="text-sm font-medium leading-none">
                  {run.profile?.name || 'Unknown Profile'}
                </p>
                <p class="text-muted-foreground text-sm">
                  <ResolvedKeywords keywordIds={run.keywords || ''} fallback="No keywords" />
                </p>
              </div>
              <div class="ml-auto">
                <span
                  class="rounded-full px-2 py-1 text-xs {run.status === 'completed'
                    ? 'bg-green-100 text-green-800'
                    : run.status === 'running'
                      ? 'bg-blue-100 text-blue-800'
                      : run.status === 'failed'
                        ? 'bg-red-100 text-red-800'
                        : 'bg-gray-100 text-gray-800'}">
                  {run.status}
                </span>
              </div>
            </div>
          {/each}
          <div class="pt-2 text-center">
            <a href="/dashboard/automation" class="text-primary text-sm hover:underline"
              >View all automation runs</a>
          </div>
        </div>
      {/if}
    </Card.Content>
  </Card.Root>

  <!-- Application Status -->
  <Card.Root class="col-span-4">
    <Card.Header>
      <Card.Title>Application Status</Card.Title>
      <Card.Description>Distribution of applications by status</Card.Description>
    </Card.Header>
    <Card.Content>
      {#if data.applicationStats?.byStatus && Object.keys(data.applicationStats.byStatus).length > 0}
        <div class="space-y-4">
          {#each Object.entries(data.applicationStats.byStatus) as [status, count]}
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div
                  class="mr-2 h-3 w-3 rounded-full {status === 'applied'
                    ? 'bg-primary'
                    : status === 'interview'
                      ? 'bg-purple-500'
                      : status === 'assessment'
                        ? 'bg-warning'
                        : status === 'offer'
                          ? 'bg-success'
                          : status === 'rejected'
                            ? 'bg-destructive'
                            : 'bg-muted'}">
                </div>
                <span class="text-sm font-medium"
                  >{status.charAt(0).toUpperCase() + status.slice(1)}</span>
              </div>
              <div class="flex items-center">
                <span class="text-muted-foreground text-sm"
                  >{count} ({Math.round((count / data.applicationStats.total) * 100)}%)</span>
              </div>
            </div>
          {/each}
          <div class="pt-2 text-center">
            <a href="/dashboard/tracker" class="text-primary text-sm hover:underline"
              >View application tracker</a>
          </div>
        </div>
      {:else}
        <div
          class="text-muted-foreground flex flex-col items-center justify-center py-6 text-center">
          <p>No application data available</p>
        </div>
      {/if}
    </Card.Content>
  </Card.Root>

  <!-- Profile Completion -->
  <Card.Root class="col-span-3">
    <Card.Header>
      <Card.Title>Profile Completion</Card.Title>
      <Card.Description>Completion status of your profiles</Card.Description>
    </Card.Header>
    <Card.Content>
      {#if data.profileStats?.length}
        <div class="space-y-4">
          {#each data.profileStats as profile}
            <div class="space-y-2">
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium">{profile.name}</span>
                <span class="text-muted-foreground text-sm">{profile.completionPercentage}%</span>
              </div>
              <div class="bg-muted h-2 w-full overflow-hidden rounded-full">
                <div
                  class="h-full rounded-full {profile.completionPercentage >= 80
                    ? 'bg-green-500'
                    : profile.completionPercentage >= 50
                      ? 'bg-yellow-500'
                      : 'bg-red-500'}"
                  style="width: {profile.completionPercentage}%">
                </div>
              </div>
              <div class="text-muted-foreground text-xs">
                {profile.documentCount} document{profile.documentCount !== 1 ? 's' : ''}
              </div>
            </div>
          {/each}
          <div class="pt-2 text-center">
            <a href="/dashboard/settings/profile" class="text-primary text-sm hover:underline"
              >Manage profiles</a>
          </div>
        </div>
      {:else}
        <div
          class="text-muted-foreground flex flex-col items-center justify-center py-6 text-center">
          <p>No profile data available</p>
          <p class="mt-1 text-sm">Create your first profile to get started</p>
        </div>
      {/if}
    </Card.Content>
  </Card.Root>
</div>

<!-- Referral Card Section -->
<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
  <div class="lg:col-span-1">
    <ReferralCard />
  </div>
</div>

<PricingModal open={showPricing} onClose={() => (showPricing = false)} />

<!-- Welcome components -->
<WelcomeToast userData={data.user} />
<WelcomeSlider
  open={showWelcomeSlider}
  onClose={() => (showWelcomeSlider = false)}
  userData={data.user} />
