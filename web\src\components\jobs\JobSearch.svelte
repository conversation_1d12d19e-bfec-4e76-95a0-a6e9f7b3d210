<script lang="ts">
  import { Sliders } from 'lucide-svelte';
  import { Input } from '$lib/components/ui/input';
  import * as Select from '$lib/components/ui/select';
  import { MultiCombobox } from '$lib/components/ui/combobox';
  import { SearchInput } from '$lib/components/ui/search-input';
  import { browser } from '$app/environment';
  import { graphqlRequest, CITIES_QUERY, COMPANIES_QUERY } from '$lib/graphql/client';
  import { debounce } from '$lib/utils';
  import { Button } from '$lib/components/ui/button';
  import * as Sheet from '$lib/components/ui/sheet';
  import * as Dialog from '$lib/components/ui/dialog';
  import { Switch } from '$lib/components/ui/switch';
  import { toast } from 'svelte-sonner';

  // Define types for our component
  interface LocationOption {
    id?: string;
    name?: string;
    stateCode?: string;
    country?: string;
    value: string;
    label: string;
  }

  interface CompanyOption {
    id?: string;
    name?: string;
    value: string;
    label: string;
  }

  interface DatePostedOption {
    value: string;
    label: string;
  }

  interface SalaryOption {
    value: string;
    label: string;
  }

  interface SearchParams {
    title?: string;
    locations?: string[];
    locationType?: string[];
    experience?: string[];
    category?: string[];
    education?: string[];
    salary?: string;
    datePosted?: string;
    companies?: string[];
    easyApply?: boolean;
    [key: string]: any;
  }

  let {
    onSearch,
    isSearching = false,
    user = null, // Add user prop to check authentication status
    initialParams = {},
  } = $props<{
    onSearch: (params: SearchParams) => Promise<any>;
    isSearching?: boolean;
    user?: any;
    initialParams?: SearchParams;
  }>();

  // Determine if user is authenticated
  const isAuthenticated = $derived(!!user);

  // Disable inputs during search
  const inputsDisabled = $derived(isSearching);

  // Sheet state for filters
  let showFiltersSheet = $state(false);

  // Save search dialog state
  let showSaveSearchDialog = $state(false);
  let searchName = $state('');
  let searchNotifications = $state(false);
  let saveAsJobAlert = $state(false);

  // Auth dialog state
  let showAuthDialog = $state(false);
  let authAction = $state('');

  let title = $state(initialParams.title || '');
  let locations = $state(
    Array.isArray(initialParams.locations)
      ? initialParams.locations
      : initialParams.locations
        ? initialParams.locations.split(',')
        : []
  );
  // Variables for locations
  let locationType = $state(
    Array.isArray(initialParams.locationType)
      ? initialParams.locationType
      : initialParams.locationType
        ? initialParams.locationType.split(',')
        : []
  );
  let experience = $state(
    Array.isArray(initialParams.experience)
      ? initialParams.experience
      : initialParams.experience
        ? initialParams.experience.split(',')
        : []
  );
  let category = $state(initialParams.category || []);
  let education = $state(initialParams.education || []);
  let salary = $state(initialParams.salary || '');
  let datePosted = $state(initialParams.datePosted || '');
  let companies = $state(
    Array.isArray(initialParams.companies)
      ? initialParams.companies
      : initialParams.companies
        ? initialParams.companies.split(',')
        : []
  );
  let easyApply = $state(initialParams.easyApply === 'true' || false);

  // Cache for cities data
  let citiesCache = $state(null);
  let citiesCacheTimestamp = $state(0);
  const CITIES_CACHE_TIMEOUT = 10 * 60 * 1000; // 10 minutes

  // Cache for companies data
  let companiesCache = $state(null);
  let companiesCacheTimestamp = $state(0);
  const COMPANIES_CACHE_TIMEOUT = 10 * 60 * 1000; // 10 minutes

  let cities = $state<LocationOption[]>([]);
  let filteredCities = $state<LocationOption[]>([]);
  let isLoadingCities = $state(false);

  const locationTypeOptions = ['Remote', 'Hybrid', 'Onsite'];
  const experienceOptions = [
    'Internship',
    'Entry Level',
    'Junior',
    'Mid Level',
    'Senior',
    'Executive',
  ];

  const datePostedOptions = [
    { value: 'any', label: 'Any Time' },
    { value: 'today', label: 'Today' },
    { value: 'week', label: 'Past Week' },
    { value: 'month', label: 'Past Month' },
    { value: '3months', label: 'Past 3 Months' },
  ];

  const salaryOptions = [
    { value: '', label: 'Any Salary' },
    { value: '0-50000', label: '$0 - $50,000' },
    { value: '50000-75000', label: '$50,000 - $75,000' },
    { value: '75000-100000', label: '$75,000 - $100,000' },
    { value: '100000-150000', label: '$100,000 - $150,000' },
    { value: '150000+', label: '$150,000+' },
  ];

  let companyOptions = $state<CompanyOption[]>([]);
  let filteredCompanyOptions = $state<CompanyOption[]>([]);
  let isLoadingCompanies = $state(false);
  let selectedCompany = $state('');

  // Computed property to check if any filters are active
  const hasActiveFilters = $derived(
    title || locations.length > 0 || locationType.length > 0 || experience.length > 0 || salary
  );

  // Check if at least one filter is filled
  const isFormValid = $derived(
    !!title || locations.length > 0 || locationType.length > 0 || experience.length > 0 || !!salary
  );

  // Flag to prevent initial search
  let initialized = $state(false);

  // Function to initialize from URL parameters
  function initFromUrlParams() {
    if (!browser) return;

    const url = new URL(window.location.href);

    // Extract parameters from URL
    const urlTitle = url.searchParams.get('title');
    if (urlTitle) title = urlTitle;

    const urlLocations = url.searchParams.get('locations');
    if (urlLocations) {
      locations = urlLocations.split(',').filter(Boolean);
    }

    const urlLocationType = url.searchParams.get('locationType');
    if (urlLocationType) {
      locationType = urlLocationType.split(',').filter(Boolean);
    }

    const urlExperience = url.searchParams.get('experience');
    if (urlExperience) {
      experience = urlExperience.split(',').filter(Boolean);
    }

    const urlSalary = url.searchParams.get('salary');
    if (urlSalary) salary = urlSalary;

    const urlDatePosted = url.searchParams.get('datePosted');
    if (urlDatePosted) datePosted = urlDatePosted;

    const urlCompanies = url.searchParams.get('companies');
    if (urlCompanies) {
      companies = urlCompanies.split(',').filter(Boolean);
    }

    const urlEasyApply = url.searchParams.get('easyApply');
    if (urlEasyApply) easyApply = urlEasyApply === 'true';
  }

  // Track initialization state
  let isInitializing = false;

  // Initialize component - wrapped in a function to prevent duplicate initialization
  function initializeComponent() {
    if (isInitializing || initialized) {
      console.log(
        'Component already initializing or initialized, skipping duplicate initialization'
      );
      return;
    }

    isInitializing = true;
    console.log('Initializing JobSearch component');

    // Initialize from URL parameters
    initFromUrlParams();

    // Use a one-time initialization approach
    // We'll fetch cities and companies only once during initialization
    // These functions now have their own caching
    console.log('Initializing cities cache:', citiesCache);
    console.log('Initializing companies cache:', companiesCache);

    try {
      fetchCities();
    } catch (error) {
      console.error('Error fetching cities during initialization:', error);
    }

    try {
      fetchCompanies();
    } catch (error) {
      console.error('Error fetching companies during initialization:', error);
    }

    // Trigger search if there are initial parameters
    setTimeout(() => {
      if (
        title ||
        locations.length > 0 ||
        locationType.length > 0 ||
        experience.length > 0 ||
        salary
      ) {
        handleSearch();
      }

      // Set initialized flag after initial load
      initialized = true;
      isInitializing = false;
      console.log('JobSearch component initialization complete');
    }, 300);
  }

  // Run initialization if in browser
  if (browser) {
    initializeComponent();

    // Listen for URL parameter updates from the parent component
    window.addEventListener('urlparamsupdated', handleUrlParamsUpdated as EventListener);
  }

  // Clean up event listeners when component is destroyed
  $effect.root(() => {
    return () => {
      if (browser) {
        window.removeEventListener('urlparamsupdated', handleUrlParamsUpdated as EventListener);
        initialized = false;
      }
    };
  });

  // We're now using debouncedSearch to handle all filter changes

  // Format salary for display
  function formatSalary(salaryValue: string): string {
    switch (salaryValue) {
      case '0-50000':
        return '$0 - $50,000';
      case '50000-75000':
        return '$50,000 - $75,000';
      case '75000-100000':
        return '$75,000 - $100,000';
      case '100000-150000':
        return '$100,000 - $150,000';
      case '150000+':
        return '$150,000+';
      default:
        return salaryValue;
    }
  }

  // Clear all filters
  function clearAllFilters() {
    title = '';
    locations = [];
    locationType = [];
    experience = [];
    salary = '';
    datePosted = '';
    companies = [];
    selectedCompany = '';
    easyApply = false;

    // Clear URL parameters
    if (browser) {
      const url = new URL(window.location.href);
      const params = new URLSearchParams();

      // Keep only non-search related parameters
      for (const [key, value] of new URLSearchParams(url.search).entries()) {
        if (
          ![
            'title',
            'locations',
            'locationType',
            'experience',
            'salary',
            'datePosted',
            'companies',
            'easyApply',
          ].includes(key)
        ) {
          params.set(key, value);
        }
      }

      const newUrl = `${url.pathname}?${params.toString()}`;
      window.history.replaceState({}, '', newUrl);
    }

    // Call onSearch directly instead of handleSearch
    // This bypasses the hasFilters check
    onSearch({
      title,
      locations,
      locationType,
      experience,
      category,
      education,
      salary,
      datePosted,
      companies: [],
      easyApply,
    });
  }

  // Function to fetch cities using GraphQL
  async function fetchCities(search = '') {
    try {
      // If we're already loading, don't start another request
      if (isLoadingCities) {
        console.log('Cities already loading, skipping duplicate request');
        return;
      }

      // Check if we have cached data and no search term
      const now = Date.now();
      if (!search && citiesCache && now - citiesCacheTimestamp < CITIES_CACHE_TIMEOUT) {
        console.log('Using cached cities data');
        cities = citiesCache;
        filteredCities = [...cities];
        return;
      }
    } catch (error) {
      console.error('Error in fetchCities initial checks:', error);
      // Continue with the function to try to recover
    }

    isLoadingCities = true;
    console.log('Fetching cities with search:', search);

    try {
      const result = await graphqlRequest(CITIES_QUERY, {
        search,
        country: 'US',
        limit: 100,
      });

      // Check if we have data in the response
      const citiesData = result.data?.locations || [];

      // Transform the data for the multi-combobox
      if (citiesData && citiesData.length > 0) {
        cities = citiesData.map((city: any) => {
          // Handle both formats (with state as object or as string)
          const stateCode = typeof city.state === 'object' ? city.state.code : city.stateCode || '';
          return {
            value: `${city.id}|${city.name}|${stateCode}|${city.country || 'US'}`,
            label: `${city.name}, ${stateCode}`,
          };
        });

        // Add some test cities if none are returned (for debugging)
        if (cities.length === 0) {
          cities = [
            { value: 'test1|New York|NY|US', label: 'New York, NY' },
            { value: 'test2|Los Angeles|CA|US', label: 'Los Angeles, CA' },
            { value: 'test3|Chicago|IL|US', label: 'Chicago, IL' },
          ];
        }
      } else {
        // If no cities are returned, add some test data for debugging
        cities = [
          { value: 'test1|New York|NY|US', label: 'New York, NY' },
          { value: 'test2|Los Angeles|CA|US', label: 'Los Angeles, CA' },
          { value: 'test3|Chicago|IL|US', label: 'Chicago, IL' },
        ];
      }

      // Create a new array to trigger reactivity
      filteredCities = [...cities];

      // Cache the cities data if this was not a search request
      if (!search) {
        citiesCache = cities;
        citiesCacheTimestamp = Date.now();
        console.log('Cached cities data');
      }

      // If we have initial locations from URL params, try to match them
      if (locations.length > 0 && cities.length > 0 && !initialized) {
        // This handles the case where locations are IDs from URL params
        const locationValues = cities
          .filter((city) => {
            return locations.some((loc: string) => {
              // Check if loc is already in the correct format
              if (loc.includes('|')) {
                return city.value === loc;
              }
              // Otherwise try to match by name or ID
              return city.value.startsWith(loc) || city.label.includes(loc);
            });
          })
          .map((city) => city.value);

        if (locationValues.length > 0) {
          locations = locationValues;
        }
      }
    } catch (error) {
      console.error('Error fetching cities:', error);
      // Use test data in case of error
      cities = [
        { value: 'test1|New York|NY|US', label: 'New York, NY' },
        { value: 'test2|Los Angeles|CA|US', label: 'Los Angeles, CA' },
        { value: 'test3|Chicago|IL|US', label: 'Chicago, IL' },
      ];
      filteredCities = [...cities];
    } finally {
      isLoadingCities = false;
    }
  }

  // Function to fetch companies using GraphQL
  async function fetchCompanies(search = '') {
    try {
      // If we're already loading, don't start another request
      if (isLoadingCompanies) {
        console.log('Companies already loading, skipping duplicate request');
        return;
      }

      // Check if we have cached data and no search term
      const now = Date.now();
      if (!search && companiesCache && now - companiesCacheTimestamp < COMPANIES_CACHE_TIMEOUT) {
        console.log('Using cached companies data');
        companyOptions = companiesCache;
        filteredCompanyOptions = [...companyOptions];
        return;
      }
    } catch (error) {
      console.error('Error in fetchCompanies initial checks:', error);
      // Continue with the function to try to recover
    }

    isLoadingCompanies = true;
    console.log('Fetching companies with search:', search);

    try {
      const result = await graphqlRequest(COMPANIES_QUERY, {
        search,
        limit: 100,
      });

      // Check if we have data in the response
      const companiesData = result.data?.companies || [];

      // Transform the data for the multi-combobox
      if (companiesData && companiesData.length > 0) {
        // Create properly formatted options for the MultiCombobox
        companyOptions = companiesData.map((company: any) => ({
          value: company.id,
          label: company.name,
        }));

        // Add some test companies if none are returned (for debugging)
        if (companyOptions.length === 0) {
          companyOptions = [
            { value: 'test1', label: 'Test Company 1' },
            { value: 'test2', label: 'Test Company 2' },
            { value: 'test3', label: 'Test Company 3' },
          ];
        }
      } else {
        // If no companies are returned, add some test data for debugging
        companyOptions = [
          { value: 'test1', label: 'Test Company 1' },
          { value: 'test2', label: 'Test Company 2' },
          { value: 'test3', label: 'Test Company 3' },
        ];
      }

      // Create a new array to trigger reactivity
      filteredCompanyOptions = [...companyOptions];

      // Cache the companies data if this was not a search request
      if (!search) {
        companiesCache = companyOptions;
        companiesCacheTimestamp = Date.now();
        console.log('Cached companies data');
      }

      // If we have initial companies, try to match them
      if (companies.length > 0 && companyOptions.length > 0 && !initialized) {
        // This handles the case where companies are IDs from URL params
        const companyValues = companyOptions
          .filter((company) => {
            return companies.some((comp: string) => {
              return company.value === comp || company.label.includes(comp);
            });
          })
          .map((company) => company.value);

        if (companyValues.length > 0) {
          companies = companyValues;
        }
      }
    } catch (error) {
      console.error('Error fetching companies:', error);
      // Use test data in case of error
      companyOptions = [
        { value: 'test1', label: 'Test Company 1' },
        { value: 'test2', label: 'Test Company 2' },
        { value: 'test3', label: 'Test Company 3' },
      ];
      // Create a new array to trigger reactivity
      filteredCompanyOptions = [...companyOptions];
    } finally {
      isLoadingCompanies = false;
    }
  }

  // Function to handle URL parameter updates from the parent component
  function handleUrlParamsUpdated(event: CustomEvent) {
    if (!initialized) return;

    // Update local state from URL parameters
    if (event.detail && event.detail.params) {
      const params = event.detail.params;

      // Update title
      if (params.title !== undefined) {
        title = params.title || '';
      }

      // Update locations
      if (params.locations) {
        try {
          const locationValues = params.locations.split(',').filter(Boolean);
          if (locationValues.length > 0) {
            locations = locationValues;
          }
        } catch (e) {
          console.error('Error parsing locations from URL event:', e);
        }
      }

      // Update locationType
      if (params.locationType) {
        try {
          const locationTypeValues = params.locationType.split(',').filter(Boolean);
          if (locationTypeValues.length > 0) {
            locationType = locationTypeValues;
          }
        } catch (e) {
          console.error('Error parsing locationType from URL event:', e);
        }
      }

      // Update experience
      if (params.experience) {
        try {
          const experienceValues = params.experience.split(',').filter(Boolean);
          if (experienceValues.length > 0) {
            experience = experienceValues;
          }
        } catch (e) {
          console.error('Error parsing experience from URL event:', e);
        }
      }

      // Update salary
      if (params.salary !== undefined) {
        salary = params.salary || '';
      }

      // Update datePosted
      if (params.datePosted !== undefined) {
        datePosted = params.datePosted || '';
      }

      // Update companies
      if (params.companies) {
        try {
          const companyValues = params.companies.split(',').filter(Boolean);
          if (companyValues.length > 0) {
            companies = companyValues;
          }
        } catch (e) {
          console.error('Error parsing companies from URL event:', e);
        }
      }

      // Update easyApply
      if (params.easyApply !== undefined) {
        easyApply = params.easyApply === 'true';
      }
    }
  }

  // Helper function to update URL parameters
  function updateUrlParam(name: string, value: string | string[]) {
    console.log(`updateUrlParam called with name: ${name}, value:`, value);

    if (!browser) {
      console.log('Browser not available, skipping URL update');
      return;
    }

    // Skip updating locations and companies if they're already being handled by the multi-combobox
    // This prevents conflicts between the two URL parameter update mechanisms
    if (
      (name === 'locations' || name === 'companies') &&
      document.querySelector(`[data-param-name="${name}"]`)
    ) {
      console.log(`JobSearch: Skipping URL param update for ${name} - handled by multi-combobox`);
      return;
    }

    // Create a search params object with all current parameters
    const searchParams = {
      title,
      locations,
      locationType,
      experience,
      category,
      education,
      salary,
      datePosted,
      companies,
      easyApply,
    };

    // Update the specific parameter
    searchParams[name] = value;

    console.log(
      `JobSearch: Calling onSearch with updated params after ${name} change:`,
      searchParams
    );

    // Update URL directly
    try {
      const url = new URL(window.location.href);
      console.log('Current URL:', url.toString());

      const params = new URLSearchParams(url.search);
      console.log('Current URL params:', Object.fromEntries(params.entries()));

      // Handle arrays by joining with commas
      if (Array.isArray(value)) {
        console.log(`Value is array with length ${value.length}`);
        if (value.length > 0) {
          const joinedValue = value.join(',');
          console.log(`Setting param ${name} to joined value: ${joinedValue}`);
          params.set(name, joinedValue);
        } else {
          console.log(`Deleting param ${name} because array is empty`);
          params.delete(name);
        }
      } else if (value) {
        console.log(`Setting param ${name} to string value: ${value}`);
        params.set(name, value);
      } else {
        console.log(`Deleting param ${name} because value is empty`);
        params.delete(name);
      }

      // Update URL without reloading the page
      const newUrl = `${url.pathname}?${params.toString()}`;
      console.log('New URL will be:', newUrl);

      window.history.pushState({}, '', newUrl);
      console.log('URL updated successfully');

      // Dispatch a custom event to notify that URL parameters have changed
      console.log('Dispatching urlparamsupdated event');
      window.dispatchEvent(
        new CustomEvent('urlparamsupdated', {
          detail: { params: Object.fromEntries(params.entries()) },
        })
      );
    } catch (error) {
      console.error('Error updating URL:', error);
    }

    // Call onSearch with the updated parameters
    // This will update the URL in the parent component
    console.log('Calling onSearch with searchParams:', searchParams);
    onSearch(searchParams);
  }

  // This code was part of the initFromUrlParams function that was refactored
  // The function is now properly defined at line 130

  // Track the last search time to prevent spamming
  let lastSearchTime = $state<number>(0);

  // Function to handle search with all parameters
  function handleSearch() {
    // Validate that at least one filter is provided
    const hasFilters =
      title ||
      locations.length > 0 ||
      locationType.length > 0 ||
      experience.length > 0 ||
      salary ||
      datePosted ||
      selectedCompany ||
      easyApply;

    if (!hasFilters && !initialized) {
      return;
    }

    // Make sure we're using the correct companies parameter
    // If we have companies from the multi-combobox, use those
    // Otherwise, fall back to selectedCompany if it exists
    const companiesParam =
      companies.length > 0 ? companies : selectedCompany ? [selectedCompany] : [];

    // Always include all parameters to ensure consistent search behavior
    const searchParams = {
      title,
      locations,
      locationType,
      experience,
      category,
      education,
      salary,
      datePosted,
      companies: companiesParam,
      easyApply,
      saveSearch: saveAsJobAlert, // Pass the saveAsJobAlert value to the parent component
    };

    // Call the debounced search function
    debouncedSearch(searchParams);
  }

  // Create a debounced version of the search function to prevent multiple rapid calls
  const debouncedSearch = debounce((searchParams: SearchParams) => {
    // Simple throttling to prevent too many requests
    const now = Date.now();
    if (now - lastSearchTime < 500) {
      return;
    }

    // Update the last search time
    lastSearchTime = now;

    // Call the parent component's onSearch function
    onSearch(searchParams);
  }, 500);

  // Function to handle actions that require authentication
  function handleAuthAction(action: string) {
    if (!isAuthenticated) {
      if (action === 'save') {
        // For job alerts, route directly to sign in page
        window.location.href = '/auth/sign-in';
        return false;
      } else {
        // For other actions, show the auth dialog
        authAction = action;
        showAuthDialog = true;
        return false;
      }
    }
    return true;
  }

  // Function to save the current search
  async function saveSearch() {
    // Check if user is authenticated
    if (!handleAuthAction('save')) return;

    if (!searchName.trim()) {
      toast.error('Please enter a name for your search');
      return;
    }

    // Create search object
    const searchData = {
      name: searchName.trim(),
      filters: {
        title,
        locations,
        locationType,
        experience,
        category,
        education,
        salary,
        datePosted,
        companies: selectedCompany ? [selectedCompany] : [],
        easyApply,
      },
      notifications: searchNotifications,
      createdAt: new Date().toISOString(),
    };

    // In a real app, this would be an API call to save the search
    console.log('Saving search:', searchData);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Close dialog and reset form
      showSaveSearchDialog = false;
      searchName = '';
      searchNotifications = false;

      // Show success message
      toast.success('Your search has been saved');
    } catch (error) {
      toast.error('Issue saving your search');
    }
  }
</script>

<div class="m-0 flex flex-row gap-2 border-b">
  <!-- Job Title -->
  <div class="relative m-0 w-full border-r px-4 py-2 md:w-1/4">
    <SearchInput
      bind:value={title}
      placeholder="Job title (e.g. Software Engineer)"
      className="border-none shadow-none drop-shadow-none"
      paramName="title"
      disabled={inputsDisabled}
      autofocus={true}
      onSearch={(value) => {
        // Only trigger search if title is at least 3 characters or empty
        if (browser && initialized && (value.length > 2 || value.length === 0)) {
          handleSearch();
        }
      }} />
  </div>

  <div class="flex w-full flex-row py-2 pr-6 md:flex-row md:space-x-2 md:space-y-0">
    <!-- Location Filter -->
    <div class="relative w-full sm:w-auto">
      <MultiCombobox
        options={filteredCities}
        bind:selectedValues={locations}
        placeholder="Select locations"
        searchPlaceholder="Search locations..."
        emptyMessage={isLoadingCities ? 'Loading...' : 'No locations found'}
        width="w-full"
        disabled={inputsDisabled}
        paramName="locations"
        onSelectedValuesChange={(values) => {
          console.log('Location values changed:', values);
          if (browser && initialized) {
            // Let the component handle the URL parameter update
            // Just trigger the search
            handleSearch();
          }
        }} />
    </div>

    <!-- Work Type Filter -->
    <div class="relative w-full sm:w-auto">
      <Select.Root
        type="multiple"
        value={locationType}
        onValueChange={(values: string[]) => {
          if (!values) {
            return;
          }

          try {
            // Update the locationType array with the selected values
            locationType = values;

            if (browser && initialized) {
              updateUrlParam('locationType', locationType);
              handleSearch();
            }
          } catch (error) {
            console.error('Error in onValueChange:', error);
          }
        }}>
        <Select.Trigger class="border-md px-4 py-2 font-light">
          <Select.Value
            placeholder={locationType.length > 0
              ? locationType.length === 1
                ? locationType[0]
                : `${locationType.length} work types selected`
              : 'Work Type'}
            onSelect={() => {
              console.log('Work Type Select.Value clicked');
            }} />
        </Select.Trigger>
        <Select.Content class="!w-[150px] rounded-none" align="start" sideOffset={3}>
          <Select.Group>
            {#each locationTypeOptions as option}
              <Select.Item value={option} class="capitalize">
                {option}
              </Select.Item>
            {/each}
          </Select.Group>
        </Select.Content>
      </Select.Root>
    </div>

    <!-- Experience Level Filter -->
    <div class="relative w-full sm:w-auto">
      <Select.Root
        type="multiple"
        value={experience}
        onValueChange={(values: string[]) => {
          if (!values) {
            return;
          }

          try {
            // Update the experience array with the selected values
            experience = values;

            if (browser && initialized) {
              updateUrlParam('experience', experience);
              handleSearch();
            }
          } catch (error) {
            console.error('Error in onValueChange:', error);
          }
        }}>
        <Select.Trigger class="border-md px-4 py-2 font-light">
          <Select.Value
            placeholder={experience.length > 0
              ? experience.length === 1
                ? experience[0]
                : `${experience.length} experience levels selected`
              : 'Experience'}
            onSelect={() => {
              console.log('Experience Select.Value clicked');
              // This will open the dropdown when the value is clicked
              // The actual selection is handled by the Select.Root onSelectedChange handler
            }} />
        </Select.Trigger>
        <Select.Content class="!w-[150px] rounded-none" align="start" sideOffset={3}>
          <Select.Group>
            {#each experienceOptions as option}
              <Select.Item value={option} class="capitalize">
                {option}
              </Select.Item>
            {/each}
          </Select.Group>
        </Select.Content>
      </Select.Root>
    </div>

    <!-- Salary Range Filter -->
    <div class="relative w-full sm:w-auto">
      <Select.Root
        type="single"
        value={salary}
        onValueChange={(value: string) => {
          try {
            salary = value || '';

            if (browser && initialized) {
              updateUrlParam('salary', salary);
              handleSearch();
            }
          } catch (error) {
            console.error('Error in onValueChange:', error);
          }
        }}>
        <Select.Trigger class="border-md px-4 py-2 font-light">
          <Select.Value placeholder={salary ? formatSalary(salary) : 'Salary'} />
        </Select.Trigger>
        <Select.Content class="!w-[200px] rounded-none" align="start" sideOffset={3}>
          {#each salaryOptions as option}
            <Select.Item value={option.value}>{option.label}</Select.Item>
          {/each}
        </Select.Content>
      </Select.Root>
    </div>

    <!-- Date Posted Filter -->
    <div class="relative w-full sm:w-auto">
      <Select.Root
        type="single"
        value={datePosted}
        onValueChange={(value: string) => {
          try {
            datePosted = value || '';

            if (browser && initialized) {
              updateUrlParam('datePosted', datePosted);
              handleSearch();
            }
          } catch (error) {
            console.error('Error in onValueChange:', error);
          }
        }}>
        <Select.Trigger class="border-md px-4 py-2 font-light">
          <Select.Value
            placeholder={datePosted
              ? datePostedOptions.find((o) => o.value === datePosted)?.label || 'Date Posted'
              : 'Date Posted'} />
        </Select.Trigger>
        <Select.Content class="!w-[150px] rounded-none" align="start" sideOffset={3}>
          {#each datePostedOptions as option}
            <Select.Item value={option.value}>{option.label}</Select.Item>
          {/each}
        </Select.Content>
      </Select.Root>
    </div>

    <!-- Company Filter -->
    <div class="relative w-full sm:w-auto">
      <MultiCombobox
        options={filteredCompanyOptions}
        bind:selectedValues={companies}
        placeholder="Select companies"
        searchPlaceholder="Search companies..."
        emptyMessage={isLoadingCompanies ? 'Loading...' : 'No companies found'}
        width="w-[250px]"
        disabled={inputsDisabled}
        paramName="companies"
        onSelectedValuesChange={(values) => {
          console.log('Companies changed:', values);
          if (browser && initialized) {
            // Let the component handle the URL parameter update
            // Just trigger the search
            console.log('Calling handleSearch with companies:', companies);
            handleSearch();
          }
        }} />
    </div>

    <!-- Easy Apply Toggle -->
    <Button
      class="border-md border p-4"
      id="easy-apply"
      onclick={() => {
        easyApply = !easyApply;
        if (browser && initialized) {
          updateUrlParam('easyApply', easyApply ? 'true' : '');
          handleSearch();
        }
      }}
      disabled={inputsDisabled}>
      Easy Apply
    </Button>

    <!-- All Filters Button -->
    <div class="relative ml-auto w-full sm:w-auto">
      <Button
        variant="outline"
        class="border-md px-4 py-2 font-light"
        disabled={inputsDisabled}
        onclick={() => (showFiltersSheet = true)}>
        <Sliders class="mr-2 h-4 w-4" />
        All Filters
        {#if title || locations.length > 0 || locationType.length > 0 || experience.length > 0 || salary || datePosted || companies.length > 0 || easyApply}
          <span
            class="bg-primary text-primary-foreground ml-2 rounded-full border px-2 py-0.5 text-xs">
            {(title ? 1 : 0) +
              (locations.length > 0 ? 1 : 0) +
              (locationType.length > 0 ? 1 : 0) +
              (experience.length > 0 ? 1 : 0) +
              (salary ? 1 : 0) +
              (datePosted ? 1 : 0) +
              (companies.length > 0 ? 1 : 0) +
              (easyApply ? 1 : 0)}
          </span>
        {/if}
      </Button>
    </div>
  </div>
</div>

<!-- All Filters Sheet -->
<Sheet.Root bind:open={showFiltersSheet}>
  <Sheet.Portal>
    <Sheet.Overlay />
    <Sheet.Content side="right" class="w-full sm:max-w-lg">
      <Sheet.Header>
        <Sheet.Title>All Filters</Sheet.Title>
        <Sheet.Description>Apply additional filters to refine your job search.</Sheet.Description>
      </Sheet.Header>

      <div class="grid gap-4 py-4">
        <!-- Job Title Search -->
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="job-title-filter" class="text-right"> Job Title </label>
          <div class="col-span-3">
            <SearchInput
              bind:value={title}
              placeholder="Enter job title"
              paramName="title"
              onSearch={() => {
                // We don't trigger search here - it will be triggered when the user clicks Apply
              }} />
          </div>
        </div>

        <!-- Location Filter -->
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="locations-filter" class="text-right"> Locations </label>
          <div class="col-span-3">
            <MultiCombobox
              options={filteredCities}
              bind:selectedValues={locations}
              placeholder="Select locations"
              searchPlaceholder="Search locations..."
              emptyMessage={isLoadingCities ? 'Loading...' : 'No locations found'}
              width="w-full"
              paramName="locations"
              onSelectedValuesChange={(values) => {
                // Let the component handle the URL parameter update
                console.log('Locations changed in filter sheet:', values);
              }} />
          </div>
        </div>

        <!-- Date Posted -->
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="date-posted" class="text-right"> Date Posted </label>
          <Select.Root
            type="single"
            value={datePosted}
            onValueChange={(value: string) => {
              datePosted = value || '';
              if (browser && initialized) {
                updateUrlParam('datePosted', datePosted);
                handleSearch();
              }
            }}>
            <Select.Trigger id="date-posted" class="col-span-3">
              <Select.Value
                placeholder={datePosted
                  ? datePostedOptions.find((o) => o.value === datePosted)?.label ||
                    'Select date range'
                  : 'Select date range'} />
            </Select.Trigger>
            <Select.Content>
              {#each datePostedOptions as option}
                <Select.Item value={option.value}>{option.label}</Select.Item>
              {/each}
            </Select.Content>
          </Select.Root>
        </div>

        <!-- Companies -->
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="companies" class="text-right"> Companies </label>
          <div class="col-span-3">
            <MultiCombobox
              options={filteredCompanyOptions}
              bind:selectedValues={companies}
              placeholder="Select companies"
              searchPlaceholder="Search companies..."
              emptyMessage={isLoadingCompanies ? 'Loading...' : 'No companies found'}
              width="w-full"
              paramName="companies"
              onSelectedValuesChange={(values) => {
                // Let the component handle the URL parameter update
                console.log('Companies changed in filter sheet:', values);
              }} />
          </div>
        </div>

        <!-- Easy Apply -->
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="easy-apply-sheet" class="text-right"> Easy Apply Only </label>
          <div class="col-span-3 flex items-center">
            <Switch
              id="easy-apply-sheet"
              checked={easyApply}
              onCheckedChange={(checked) => {
                easyApply = checked;
                if (browser && initialized) {
                  updateUrlParam('easyApply', easyApply ? 'true' : '');
                  handleSearch();
                }
              }} />
          </div>
        </div>

        <!-- Job Type Filter -->
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="job-type" class="text-right"> Job Type </label>
          <Select.Root
            type="multiple"
            value={locationType}
            onValueChange={(values: string[]) => {
              if (!values) return;
              locationType = values;
              if (browser && initialized) {
                updateUrlParam('locationType', locationType);
                handleSearch();
              }
            }}>
            <Select.Trigger id="job-type" class="col-span-3">
              <Select.Value placeholder="Select job types" />
            </Select.Trigger>
            <Select.Content>
              <Select.Group>
                {#each locationTypeOptions as option}
                  <Select.Item value={option} class="capitalize">
                    {option}
                  </Select.Item>
                {/each}
              </Select.Group>
            </Select.Content>
          </Select.Root>
        </div>

        <!-- Experience Level Filter -->
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="experience-level" class="text-right"> Experience Level </label>
          <Select.Root
            type="multiple"
            value={experience}
            onValueChange={(values: string[]) => {
              if (!values) return;
              experience = values;
              if (browser && initialized) {
                updateUrlParam('experience', experience);
                handleSearch();
              }
            }}>
            <Select.Trigger id="experience-level" class="col-span-3">
              <Select.Value placeholder="Select experience levels" />
            </Select.Trigger>
            <Select.Content>
              <Select.Group>
                {#each experienceOptions as option}
                  <Select.Item value={option} class="capitalize">
                    {option}
                  </Select.Item>
                {/each}
              </Select.Group>
            </Select.Content>
          </Select.Root>
        </div>

        <!-- Salary Range Filter -->
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="salary-range" class="text-right"> Salary Range </label>
          <Select.Root
            type="single"
            value={salary}
            onValueChange={(value: string) => {
              salary = value || '';

              if (browser && initialized) {
                updateUrlParam('salary', salary);
                handleSearch();
              }
            }}>
            <Select.Trigger id="salary-range" class="col-span-3">
              <Select.Value placeholder="Select salary range" />
            </Select.Trigger>
            <Select.Content>
              {#each salaryOptions as option}
                <Select.Item value={option.value}>{option.label}</Select.Item>
              {/each}
            </Select.Content>
          </Select.Root>
        </div>

        <!-- Companies Filter -->
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="companies-filter" class="text-right"> Companies </label>
          <div class="col-span-3">
            <MultiCombobox
              options={filteredCompanyOptions}
              bind:selectedValues={companies}
              placeholder="Select companies"
              searchPlaceholder="Search companies..."
              emptyMessage={isLoadingCompanies ? 'Loading...' : 'No companies found'}
              width="w-full"
              paramName="companies"
              onSelectedValuesChange={(values) => {
                // Let the component handle the URL parameter update
                console.log('Companies changed in filter sheet:', values);
              }} />
          </div>
        </div>

        <!-- Easy Apply Toggle -->
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="easy-apply-toggle" class="text-right"> Easy Apply </label>
          <div class="col-span-3">
            <Switch
              id="easy-apply-toggle"
              checked={easyApply}
              onCheckedChange={(checked) => {
                easyApply = checked;
                if (browser && initialized) {
                  updateUrlParam('easyApply', easyApply ? 'true' : '');
                  handleSearch();
                }
              }} />
          </div>
        </div>
      </div>

      <Sheet.Footer class="flex justify-between">
        <Button
          variant="outline"
          onclick={() => {
            clearAllFilters();
            showFiltersSheet = false;
          }}>
          Clear Filters
        </Button>
        <Button
          variant="default"
          onclick={() => {
            showFiltersSheet = false;
            browser && initialized && handleSearch();
          }}>
          Apply Filters
        </Button>
      </Sheet.Footer>
    </Sheet.Content>
  </Sheet.Portal>
</Sheet.Root>

<!-- Save Search Dialog -->
<Dialog.Root bind:open={showSaveSearchDialog}>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="sm:max-w-md">
      <Dialog.Header>
        <Dialog.Title>Save Search</Dialog.Title>
        <Dialog.Description>
          Save your current search criteria to quickly access it later.
        </Dialog.Description>
      </Dialog.Header>

      <div class="grid gap-4 py-4">
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="search-name" class="text-right"> Search Name </label>
          <Input
            id="search-name"
            placeholder="e.g., Software Engineer in NYC"
            class="col-span-3"
            bind:value={searchName} />
        </div>

        <div class="grid grid-cols-4 items-center gap-4">
          <label for="notifications" class="text-right"> Email Notifications </label>
          <div class="col-span-3 flex items-center space-x-2">
            <Switch
              id="notifications"
              checked={searchNotifications}
              onCheckedChange={(checked) => {
                searchNotifications = checked;
              }} />
            <span class="text-muted-foreground text-sm">
              Receive daily emails with new job matches
            </span>
          </div>
        </div>
      </div>

      <Dialog.Footer class="sm:justify-between">
        <Button
          variant="outline"
          onclick={() => {
            showSaveSearchDialog = false;
            searchName = '';
            searchNotifications = false;
          }}>
          Cancel
        </Button>
        <Button variant="default" onclick={saveSearch}>Save</Button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>

<!-- Authentication Dialog -->
<Dialog.Root bind:open={showAuthDialog}>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="sm:max-w-md">
      <Dialog.Header>
        <Dialog.Title>Sign in required</Dialog.Title>
        <Dialog.Description>
          {#if authAction === 'save'}
            You need to sign in to save job searches and receive alerts.
          {:else if authAction === 'apply'}
            You need to sign in to apply for jobs and track your applications.
          {:else}
            You need to sign in to access this feature.
          {/if}
        </Dialog.Description>
      </Dialog.Header>
      <div class="flex justify-end gap-4">
        <Button
          variant="outline"
          onclick={() => {
            showAuthDialog = false;
          }}>
          Cancel
        </Button>
        <Button
          variant="default"
          onclick={() => {
            window.location.href = '/auth/sign-in';
          }}>
          Sign In
        </Button>
      </div>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>
