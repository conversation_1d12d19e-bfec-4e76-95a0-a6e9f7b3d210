<script lang="ts">
  import { Sun, Moon, Laptop } from 'lucide-svelte';
  import { Button } from '$lib/components/ui/button';
  import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
  } from '$lib/components/ui/dropdown-menu';
  import { mode, setMode } from 'mode-watcher';

  // Accept props for styling
  const { variant = 'default', size = 'default' } = $props<{
    variant?: 'default' | 'outline' | 'ghost';
    size?: 'default' | 'sm' | 'lg' | 'icon';
  }>();

  // Get current theme from mode-watcher
  const currentTheme = $derived($mode || 'system');

  // Handle theme change
  function handleThemeChange(theme: 'light' | 'dark' | 'system') {
    setMode(theme);
  }
</script>

<DropdownMenu>
  <DropdownMenuTrigger>
    <Button {variant} {size} class="gap-2">
      {#if currentTheme === 'light'}
        <Sun class="h-4 w-4" />
        {#if size !== 'icon'}
          <span>Light</span>
        {/if}
      {:else if currentTheme === 'dark'}
        <Moon class="h-4 w-4" />
        {#if size !== 'icon'}
          <span>Dark</span>
        {/if}
      {:else}
        <Laptop class="h-4 w-4" />
        {#if size !== 'icon'}
          <span>System</span>
        {/if}
      {/if}
      <span class="sr-only">Toggle theme</span>
    </Button>
  </DropdownMenuTrigger>
  <DropdownMenuContent align="end">
    <DropdownMenuItem onclick={() => handleThemeChange('light')}>
      <Sun class="mr-2 h-4 w-4" />
      <span>Light</span>
    </DropdownMenuItem>
    <DropdownMenuItem onclick={() => handleThemeChange('dark')}>
      <Moon class="mr-2 h-4 w-4" />
      <span>Dark</span>
    </DropdownMenuItem>
    <DropdownMenuItem onclick={() => handleThemeChange('system')}>
      <Laptop class="mr-2 h-4 w-4" />
      <span>System</span>
    </DropdownMenuItem>
  </DropdownMenuContent>
</DropdownMenu>
